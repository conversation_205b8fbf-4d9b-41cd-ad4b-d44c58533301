import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Toolt<PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { getNPLDonutData } from './helpers';
import numeral from 'numeral';
import CountLegend from './legends/CountLegend';

// Theme-aware color palette
const getChartColors = () => {
    const root = document.documentElement;
    const computedStyle = getComputedStyle(root);

    return [
        `hsl(${computedStyle.getPropertyValue('--chart-1').trim()})`,
        `hsl(${computedStyle.getPropertyValue('--chart-2').trim()})`,
        `hsl(${computedStyle.getPropertyValue('--chart-3').trim()})`,
        `hsl(${computedStyle.getPropertyValue('--chart-4').trim()})`,
        `hsl(${computedStyle.getPropertyValue('--chart-5').trim()})`,
    ];
};


const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, index }) => {
    const radius = innerRadius + (outerRadius - innerRadius) * 0.1;
    const x = cx + radius * Math.cos(-midAngle * Math.PI / 180);
    const y = cy + radius * Math.sin(-midAngle * Math.PI / 180);

    return (
        <text x={x} y={y} fontSize={10} fontWeight={'500'} fill="white" textAnchor={x > cx ? 'start' : 'end'} dominantBaseline="central">
            {`${(percent * 100).toFixed(0)}%`}
        </text>
    );
};

const DonutChart = ({ data, showRatio, ratioIndexToShow, legendComponent: CustomLegend = CountLegend }) => {
    const chartColors = getChartColors();

    // Assign theme colors to data if not already provided
    const dataWithThemeColors = data?.map((entry, index) => ({
        ...entry,
        color: entry.color || chartColors[index % chartColors.length]
    }));

    const getRatioToShow = () => {
        let total = 0
        dataWithThemeColors?.forEach((d) => {
            total += d.value
        })

        return dataWithThemeColors ? (dataWithThemeColors[ratioIndexToShow]?.value) / total : 0
    }

    return (
        <>
            <ResponsiveContainer className={'relative'} width="100%" height={180}>
                {showRatio &&
                    <div className='absolute w-full h-full text-2xl font-semibold flex items-center justify-center text-foreground'>
                        {numeral(getRatioToShow()).format("0.00%")}
                    </div>
                }
                <PieChart>
                    <Pie
                        data={dataWithThemeColors}
                        cx={'50%'}
                        cy={'50%'}
                        innerRadius={70}
                        outerRadius={90}
                        paddingAngle={0}
                        dataKey="value"
                        stroke='none'
                        labelLine={false}
                        label={renderCustomizedLabel}
                    >
                        {dataWithThemeColors?.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                    </Pie>
                    <Tooltip
                        contentStyle={{
                            backgroundColor: 'hsl(var(--popover))',
                            border: '1px solid hsl(var(--border))',
                            borderRadius: '6px',
                            color: 'hsl(var(--popover-foreground))'
                        }}
                    />
                </PieChart>
            </ResponsiveContainer>
            <div className='flex flex-col gap-1 mt-2'>
                {dataWithThemeColors?.map((item, index) => (
                    <CustomLegend index={index} {...item} key={item.name} />
                ))}
            </div>
        </>
    );
};

export default DonutChart;
