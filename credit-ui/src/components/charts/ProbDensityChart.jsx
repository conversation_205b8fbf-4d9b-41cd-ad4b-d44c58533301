import numeral from 'numeral';
import React from 'react';
import { AreaChart, Area, XAxis, YAxis, Tooltip, ResponsiveContainer, CartesianGrid, Legend } from 'recharts';

function formatNumber(value) {
    return numeral(value).format('0a');  // Format to "K" for thousands, "M" for millions
}

// Theme-aware color palette
const getChartColors = () => {
    const root = document.documentElement;
    const computedStyle = getComputedStyle(root);

    return [
        `hsl(${computedStyle.getPropertyValue('--chart-1').trim()})`,
        `hsl(${computedStyle.getPropertyValue('--chart-2').trim()})`,
        `hsl(${computedStyle.getPropertyValue('--chart-3').trim()})`,
        `hsl(${computedStyle.getPropertyValue('--chart-4').trim()})`,
        `hsl(${computedStyle.getPropertyValue('--chart-5').trim()})`,
    ];
};

export default function ProbDensityChart({ data, hideY, grid, height = 250 }) {
    const chartColors = getChartColors();

    return (
        <ResponsiveContainer width="100%" height={height}>
            <AreaChart height={250}>
                {grid &&
                    <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                }
                <XAxis
                    className='text-xs font-semibold'
                    tickFormatter={formatNumber}
                    dataKey="x"
                    type="number"
                    tick={{ fill: 'hsl(var(--muted-foreground))' }}
                />
                {!hideY && (
                    <YAxis
                        className='text-xs font-semibold'
                        tick={{ fill: 'hsl(var(--muted-foreground))' }}
                    />
                )}
                <Tooltip
                    contentStyle={{
                        backgroundColor: 'hsl(var(--popover))',
                        border: '1px solid hsl(var(--border))',
                        borderRadius: '6px',
                        color: 'hsl(var(--popover-foreground))'
                    }}
                />
                <Legend
                    verticalAlign="top"
                    layout="centric"
                    align="right"
                    wrapperStyle={{
                        fontSize: '0.77rem',
                        fontWeight: "600",
                        color: 'hsl(var(--foreground))'
                    }}
                />

                {/* Area plot for Non-Performing (Defaults) */}
                <Area
                    type="monotone"
                    dataKey="y"
                    data={data[0].data}  // Data for Non-Performing
                    name={data[0].name}  // Name for the legend
                    stroke={data[0].color || chartColors[0]}  // Stroke color for the curve
                    fill={data[0].color || chartColors[0]}  // Fill color for the area
                    fillOpacity={0.6}
                    dot={false}
                />

                {/* Area plot for Performing (Non-Defaults) */}
                <Area
                    type="monotone"
                    dataKey="y"
                    data={data[1].data}  // Data for Performing
                    name={data[1].name}  // Name for the legend
                    stroke={data[1].color || chartColors[1]}  // Stroke color for the curve
                    fill={data[1].color || chartColors[1]}  // Fill color for the area
                    fillOpacity={0.6}
                    dot={false}
                />
            </AreaChart>
        </ResponsiveContainer>
    );
};