import React, { useState } from 'react';

const MiniSideNav = ({selected, setSelected, navItems}) => {

  return (
    <div className="flex flex-col bg-card border border-border shadow rounded-l overflow-hidden justify-center">
      {navItems.map((item) => (
        <div
          key={item}
          onClick={() => setSelected(item)}
          className={`px-5 py-3 ${
            selected === item
              ? 'bg-primary/10 border-2 border-primary text-foreground font-medium rounded-l'
              : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
          } cursor-pointer w-full text-sm transition-colors`}
        >
          {item}
        </div>
      ))}
    </div>
  );
};

export default MiniSideNav;