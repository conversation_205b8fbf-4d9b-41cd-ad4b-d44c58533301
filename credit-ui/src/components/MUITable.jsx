import * as React from "react";
import { useMemo, useState } from "react";
import { generateName } from "../helpers";
import { mappings } from "../constants";
import Button from "./Button";

const defaultColumns = [
  { field: "id", headerName: "ID", width: 100 },
  {
    field: "full_name",
    headerName: "Full name",
    description: "This column has a value getter and is not sortable.",
    sortable: false,
    width: 260,
    valueGetter: (value, row) => `${generateName(row.person_id)}`,
  },
  {
    field: "credit_amount",
    headerName: "Loan amount (GHS)",
    width: 230,
    type: 'number',
    valueGetter: (value, row) => `${row.credit_amount.toFixed(2)}`,
  },
  {
    field: "duration",
    headerName: "Loan duration (months)",
    width: 230,
    type: 'number',
  },
  {
    field: "purpose",
    headerName: "Purpose",
    width: 190,
    valueGetter: (value, row) => `${mappings[row.purpose]}`,
  },
];

export default function MUIDataTable({
  rows,
  columns = defaultColumns,
  rowKeysToShow = [],
  onRowClick,
  pageSize = 6
}) {
  const [currentPage, setCurrentPage] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(pageSize);

  const updated_rows = useMemo(() => {
    return rows.map((row, index) => {
      const newRow = {};
      newRow.id = index;
      if (!rowKeysToShow.length) {
        rowKeysToShow = Object.keys(row);
      }
      Object.keys(row).forEach((key) => {
        if (rowKeysToShow.includes(key)) {
          newRow[key] = row[key];
        }
      });
      return newRow;
    });
  }, [rows, rowKeysToShow]);

  const totalPages = Math.ceil(updated_rows.length / itemsPerPage);
  const startIndex = currentPage * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentRows = updated_rows.slice(startIndex, endIndex);

  const getColumnValue = (row, column) => {
    if (column.valueGetter) {
      return column.valueGetter(row[column.field], row);
    }
    return mappings[row[column.field]] || row[column.field];
  };

  return (
    <div className="bg-card border border-border rounded-lg overflow-hidden">
      <div className="overflow-x-auto">
        <table className="w-full caption-bottom text-sm">
          <thead>
            <tr className="border-b border-border bg-muted/50">
              {columns.map((column) => (
                <th
                  key={column.field}
                  className="h-12 px-4 text-left align-middle font-medium text-muted-foreground"
                  style={{ minWidth: column.width || 'auto' }}
                >
                  {column.headerName}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {currentRows.map((row, index) => (
              <tr
                key={row.id}
                onClick={() => onRowClick && onRowClick({ row })}
                className="border-b border-border transition-colors hover:bg-muted/50 cursor-pointer"
              >
                {columns.map((column) => (
                  <td key={column.field} className="p-4 align-middle">
                    {getColumnValue(row, column)}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between px-4 py-3 border-t border-border bg-muted/25">
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">Rows per page:</span>
          <select
            value={itemsPerPage}
            onChange={(e) => {
              setItemsPerPage(Number(e.target.value));
              setCurrentPage(0);
            }}
            className="h-8 w-16 rounded border border-input bg-background px-2 text-sm"
          >
            <option value={6}>6</option>
            <option value={10}>10</option>
            <option value={25}>25</option>
            <option value={50}>50</option>
          </select>
        </div>

        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">
            {startIndex + 1}-{Math.min(endIndex, updated_rows.length)} of {updated_rows.length}
          </span>
          <div className="flex items-center gap-1">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(0)}
              disabled={currentPage === 0}
            >
              First
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(currentPage - 1)}
              disabled={currentPage === 0}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={currentPage === totalPages - 1}
            >
              Next
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(totalPages - 1)}
              disabled={currentPage === totalPages - 1}
            >
              Last
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
