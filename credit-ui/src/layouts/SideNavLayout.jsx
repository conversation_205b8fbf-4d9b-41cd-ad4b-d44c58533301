import {
  faArrowLeft,
  faChartGantt,
  faChartSimple,
  faDashboard,
  faGreaterThan,
  faLessThan,
  faPerson,
  faUser,
  faUsers,
  faUsersRays,
} from "@fortawesome/free-solid-svg-icons";
import { Bs<PERSON>illBellFill, BsP<PERSON>ple, <PERSON>s<PERSON><PERSON> } from "react-icons/bs";
import { TbAnalyze, TbDashboard, TbSettingsFilled } from "react-icons/tb";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import React, { useEffect, useState } from "react";
import NavItem from "../components/NavItem";
import { useLocation, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { setCurrentSideNavSection, toggleNavbar } from "../store/navSlice";
import { BiMoneyWithdraw, BiPackage, BiSearch } from "react-icons/bi";
import { HiViewGrid } from "react-icons/hi";
import AppInput from "../components/AppInput";

const sidenavs = [
  {
    text: "Dashboard",
    icon: HiViewGrid,
    path: "/dashboard",
  },
  {
    text: "Applicants",
    icon: BsPeople,
    path: "/applicants",
  },
  {
    text: "Loans",
    icon: BiMoneyWithdraw,
    path: "/loans",
  },
  {
    text: "Products",
    icon: BiPackage,
    path: "/products",
  },
];

const othernavs = [
  {
    text: "Applicant Analysis",
    path: "/analysis",
  },
];

export default function SideNavLayout({ children }) {
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const dispatch = useDispatch();
  const currentSideNavSection = useSelector((state) => state.nav.currentSideNavSection);
  const isCollapsed = useSelector((state) => state.nav.collapsed);

  const toggle = () => {
    dispatch(toggleNavbar());
  };

  useEffect(() => {
    sidenavs.forEach((sidenav) => {
      if (sidenav.path === pathname) {
        dispatch(setCurrentSideNavSection(sidenav.text))
      }
    })
  }, [pathname])

  return (
    <div className="flex h-screen w-full bg-background">
      {/* Sidebar */}
      <div
        className={`flex flex-col bg-sidebar text-sidebar-foreground border-r border-sidebar-border transition-all duration-300 ${
          isCollapsed ? "w-16" : "w-64"
        }`}
      >
        {/* Header */}
        <div
          onClick={toggle}
          className="flex items-center gap-3 p-4 border-b border-sidebar-border cursor-pointer hover:bg-sidebar-accent/10 transition-colors"
        >
          <div className="flex items-center justify-center w-8 h-8 bg-sidebar-primary rounded-lg">
            <TbAnalyze className="text-xl text-sidebar-primary-foreground" />
          </div>
          {!isCollapsed && (
            <div className="flex flex-col">
              <div className="font-semibold text-sidebar-foreground">Credit Analytics</div>
              <div className="text-xs text-sidebar-foreground/70">Dashboard</div>
            </div>
          )}
        </div>

        {/* Navigation */}
        <div className="flex-1 flex flex-col p-2">
          {!isCollapsed && (
            <div className="text-xs font-medium text-sidebar-foreground/70 px-3 py-2 mb-1">
              Main
            </div>
          )}
          <nav className="space-y-1">
            {sidenavs.map((nav, index) => (
              <NavItem
                key={index}
                isCollapsed={isCollapsed}
                onClick={() => {
                  navigate(nav.path);
                }}
                currentSideNavSection={currentSideNavSection}
                {...nav}
              />
            ))}
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border bg-card">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            {[...sidenavs].find((nav) => nav.path === pathname) && (
              <>
                <span>Pages</span>
                <span>/</span>
                <span className="text-foreground font-medium">
                  {[...sidenavs].find((nav) => nav.path === pathname)?.text}
                </span>
              </>
            )}
            {[...othernavs].find((nav) => nav.path === pathname) && (
              <>
                <button
                  onClick={() => navigate(-1)}
                  className="p-1 hover:bg-muted rounded-md transition-colors"
                >
                  <FontAwesomeIcon icon={faArrowLeft} className="text-sm" />
                </button>
                <span>Pages</span>
                <span>/</span>
                <span>{currentSideNavSection}</span>
                <span>/</span>
                <span className="text-foreground font-medium">
                  {[...othernavs].find((nav) => nav.path === pathname)?.text}
                </span>
              </>
            )}
          </div>

          <div className="flex items-center gap-4">
            <AppInput icon={BiSearch} placeholder="Type here..." />
            <div className="flex items-center gap-2 text-sm font-medium text-foreground">
              <BsPerson className="text-lg" />
              <span>Ebo</span>
            </div>
            <div className="flex items-center gap-3 text-lg text-muted-foreground">
              <button className="p-1 hover:bg-muted rounded-md transition-colors">
                <TbSettingsFilled />
              </button>
              <button className="p-1 hover:bg-muted rounded-md transition-colors">
                <BsFillBellFill />
              </button>
            </div>
          </div>
        </div>

        {/* Page Title */}
        <div className="p-6 pb-4">
          <h1 className="text-2xl font-bold text-foreground">
            {[...sidenavs, ...othernavs].find((nav) => nav.path === pathname)?.text}
          </h1>
        </div>

        {/* Page Content */}
        <div className="flex-1 overflow-y-auto p-6 pt-0 bg-background">
          {children}
        </div>
      </div>
    </div>
  );
}
