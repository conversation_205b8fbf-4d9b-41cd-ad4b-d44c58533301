{"name": "credit-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@fontsource/ibm-plex-mono": "^5.0.14", "@fontsource/ibm-plex-sans": "^5.0.21", "@fontsource/ibm-plex-serif": "^5.0.14", "@fortawesome/fontawesome-svg-core": "^6.5.1", "@fortawesome/free-solid-svg-icons": "^6.5.1", "@fortawesome/react-fontawesome": "^0.2.0", "@mui/material": "^5.16.1", "@mui/x-data-grid": "^7.10.0", "@reduxjs/toolkit": "^2.2.6", "axios": "^1.6.5", "d3": "^7.9.0", "date-fns": "^3.6.0", "firebase": "^11.0.1", "formik": "^2.4.5", "numeral": "^2.0.6", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-dom": "^18.2.0", "react-icons": "^5.3.0", "react-lottie": "^1.2.4", "react-redux": "^9.1.2", "react-router-dom": "^6.21.2", "react-switch": "^7.0.0", "react-to-print": "^2.15.1", "react-toastify": "^10.0.3", "react-tooltip": "^5.27.1", "recharts": "^2.12.7", "simple-statistics": "^7.8.3", "yup": "^1.3.3", "yup-phone-lite": "^2.0.1"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "vite": "^5.0.8"}}